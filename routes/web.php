<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Manager\ManagerDashboardController;
use App\Http\Controllers\FieldOfficer\FieldOfficerDashboardController;
use App\Http\Controllers\Member\MemberDashboardController;

// Public Routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes
Auth::routes();

// Authenticated Routes
Route::middleware(['auth'])->group(function () {
    // General Dashboard (redirects to role-specific dashboard)
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Admin Routes
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

        // Add more admin routes here as needed
        // Route::resource('users', UserController::class);
        // Route::resource('branches', BranchController::class);
    });

    // Manager Routes
    Route::middleware(['role:manager', 'branch.access'])->prefix('manager')->name('manager.')->group(function () {
        Route::get('/dashboard', [ManagerDashboardController::class, 'index'])->name('dashboard');

        // Add more manager routes here as needed
        // Route::resource('members', MemberController::class);
        // Route::resource('loan-applications', LoanApplicationController::class);
    });

    // Field Officer Routes
    Route::middleware(['role:field_officer', 'branch.access'])->prefix('field-officer')->name('field-officer.')->group(function () {
        Route::get('/dashboard', [FieldOfficerDashboardController::class, 'index'])->name('dashboard');

        // Add more field officer routes here as needed
        // Route::resource('collections', CollectionController::class);
        // Route::resource('visits', VisitController::class);
    });

    // Member Routes
    Route::middleware(['role:member'])->prefix('member')->name('member.')->group(function () {
        Route::get('/dashboard', [MemberDashboardController::class, 'index'])->name('dashboard');

        // Add more member routes here as needed
        // Route::resource('loan-applications', MemberLoanApplicationController::class);
        // Route::get('/savings', [MemberSavingsController::class, 'index'])->name('savings');
    });
});

// Legacy route redirect
Route::get('/home', function () {
    return redirect()->route('dashboard');
})->middleware('auth');
